app:
  debug: false
  log_level: INFO
  name: Reverie Agents
  version: 1.0.0
generation:
  image:
    guidance_scale: 7.5
    height: 1024
    steps: 20
    width: 1024
  max_tokens: 819200
  temperature: 0.7
  top_p: 0.9
logging:
  backup_count: 5
  log_path: logs
  max_log_size: 10MB
memory:
  context_window: 8000
  enable_long_term_memory: true
  max_memory_size: 10000
  memory_compression: true
  save_conversations: true
  storage_path: memory/storage
mirrors:
  huggingface: https://hf-mirror.com
models:
  llm:
    default_load_mode: auto
    default_model: lucy-128k
    max_context_length: 131072
    max_new_tokens: -1
    model_path: models/llm
    n_batch: 512
    n_gpu_layers: -1
    n_threads: 4
    repetition_penalty: 1.1
    server_url: http://localhost:8080/v1
    temperature: 0.7
    top_k: 40
    top_p: 0.9
    use_mlock: false
    use_mmap: true
  t2i:
    auto_unload: true
    default_guidance: 7.5
    default_height: 1024
    default_model: flux-dev
    default_sampler: euler
    default_steps: 20
    default_width: 1024
    load_mode: auto
    memory_optimization: true
    model_path: models/t2i
network:
  proxy: null
  timeout: 30
  user_agent: Reverie-Agents/1.0.0
personas:
  config_path: personas/configs
  default_persona: assistant
  enable_nsfw: true
search:
  auto_search: true
  engine: duckduckgo
  max_results: 5
  search_timeout: 30
security:
  enable_content_filter: false
  max_image_size: 10MB
ui:
  avatars:
    user: assets\avatars\user_avatar.png
  font_family: Microsoft YaHei UI
  font_size: 12
  theme: dark
  window_height: 996
  window_width: 1707
