"""
LLM模型管理器
支持多种模型加载方式：
1. 通过 llama.cpp 服务器（客户端模式）
2. 直接使用 llama-cpp-python 加载 GGUF 模型（本地模式）
"""

import os
import yaml
import threading
import struct
from typing import Optional, Dict, Any, Generator, List, Tuple, Union
from pathlib import Path
import logging
import platform
import traceback
import gc
from dataclasses import dataclass
from enum import Enum

# 使用 `openai` 库与 llama.cpp 服务器进行交互
from openai import OpenAI

from ..utils.logger import get_logger

logger = get_logger(__name__)

class ModelFormat(Enum):
    """模型格式枚举"""
    GGUF = "gguf"
    SAFETENSORS = "safetensors"
    PYTORCH = "pytorch"
    UNKNOWN = "unknown"

class LoadMode(Enum):
    """模型加载模式枚举"""
    SERVER = "server"  # 通过llama.cpp服务器
    LOCAL = "local"    # 直接本地加载
    AUTO = "auto"      # 自动选择

class ModelFormatDetector:
    """模型格式检测器"""

    @staticmethod
    def detect_format(file_path: str) -> ModelFormat:
        """检测模型文件格式"""
        if not os.path.exists(file_path):
            return ModelFormat.UNKNOWN

        file_path = Path(file_path)

        # 首先通过文件扩展名判断
        extension = file_path.suffix.lower()
        if extension == '.gguf':
            return ModelFormat.GGUF
        elif extension == '.safetensors':
            return ModelFormat.SAFETENSORS
        elif extension in ['.bin', '.pt', '.pth']:
            return ModelFormat.PYTORCH

        # 如果扩展名不明确，通过文件头判断
        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)

                # GGUF文件头检测 (GGUF magic number)
                if len(header) >= 4 and header[:4] == b'GGUF':
                    return ModelFormat.GGUF

                # SafeTensors文件头检测
                if len(header) >= 8:
                    try:
                        # SafeTensors文件开头是一个8字节的长度字段
                        header_len = struct.unpack('<Q', header[:8])[0]
                        if 0 < header_len < 1024 * 1024:  # 合理的头部长度
                            return ModelFormat.SAFETENSORS
                    except:
                        pass

                # PyTorch文件头检测 (通常以PK开头，因为是zip格式)
                if len(header) >= 2 and header[:2] == b'PK':
                    return ModelFormat.PYTORCH

        except Exception as e:
            logger.warning(f"检测文件格式时出错: {e}")

        return ModelFormat.UNKNOWN

    @staticmethod
    def get_recommended_load_mode(format: ModelFormat) -> LoadMode:
        """根据模型格式推荐加载模式"""
        if format == ModelFormat.GGUF:
            return LoadMode.LOCAL  # GGUF优先使用本地加载
        elif format in [ModelFormat.SAFETENSORS, ModelFormat.PYTORCH]:
            return LoadMode.SERVER  # 其他格式使用服务器模式
        else:
            return LoadMode.AUTO  # 未知格式自动选择

    @staticmethod
    def is_supported_for_local_loading(format: ModelFormat) -> bool:
        """检查格式是否支持本地加载"""
        return format == ModelFormat.GGUF

@dataclass
class ModelConfig:
    """模型配置类"""
    name: str
    path: str  # 模型文件路径
    format: ModelFormat = ModelFormat.UNKNOWN
    load_mode: LoadMode = LoadMode.AUTO
    context_length: int = 8000
    temperature: float = 0.7
    top_p: float = 0.9
    top_k: int = 40
    repetition_penalty: float = 1.1
    max_tokens: int = 2048
    # GGUF本地加载专用参数
    n_gpu_layers: int = -1
    n_threads: int = 4
    n_batch: int = 512
    use_mmap: bool = True
    use_mlock: bool = False

class LocalGGUFLoader:
    """本地GGUF模型加载器"""

    def __init__(self):
        self.llama_model = None
        self.model_lock = threading.Lock()
        self.backend_initialized = False
        self.last_effective_kwargs: Optional[Dict[str, Any]] = None

    def load_model(self, config: ModelConfig) -> bool:
        """加载GGUF模型"""
        try:
            # 动态导入llama_cpp，避免在不需要时导入
            import llama_cpp as _llama_mod
            Llama = _llama_mod.Llama
            # 记录当前 llama-cpp-python 版本，便于排查兼容性
            try:
                _llama_ver = getattr(_llama_mod, "__version__", "unknown")
                logger.info(f"llama-cpp-python 版本: {_llama_ver}")
            except Exception:
                pass

            # 后端初始化（若可用，主要针对某些版本/平台的稳定性）
            try:
                _is_windows = platform.system().lower().startswith("win")
                if _is_windows:
                    logger.debug("Windows 平台，跳过 llama_backend_init() 以避免潜在崩溃")
                elif not self.backend_initialized and hasattr(_llama_mod, "llama_backend_init"):
                    _llama_mod.llama_backend_init()
                    self.backend_initialized = True
                    logger.debug("llama.cpp 后端已初始化")
            except Exception as e_init:
                logger.warning(f"llama.cpp 后端初始化失败（忽略继续）: {e_init}")

            # 解析并校验模型路径
            abs_model_path = os.path.abspath(config.path)
            if not os.path.exists(abs_model_path):
                logger.error(f"模型文件不存在: {abs_model_path}")
                return False
            # 将路径规范化为POSIX格式，避免某些平台/版本在反斜杠路径上异常
            abs_model_path_posix = Path(abs_model_path).as_posix()

            # 预校验 GGUF 头和版本信息，尽早发现损坏或不兼容
            try:
                valid, gguf_ver = self._pre_validate_gguf(abs_model_path)
                file_size_gb = os.path.getsize(abs_model_path) / (1024**3)
                if valid:
                    logger.info(f"GGUF文件校验通过: 版本={gguf_ver}, 大小={file_size_gb:.2f} GB")
                else:
                    logger.error("GGUF文件头无效或版本无法识别，请确认模型文件有效")
                    return False
            except Exception as e_hdr:
                logger.warning(f"GGUF文件预校验失败（忽略继续）: {e_hdr}")

            with self.model_lock:
                # 释放之前的模型
                if self.llama_model:
                    del self.llama_model
                    self.llama_model = None

                logger.info(f"正在加载GGUF模型: {config.path}")

                # 参数化尝试策略，以提高在 Windows 等平台上的加载稳定性
                # 策略是：从最保守的参数开始，逐步放宽限制，并避免重复尝试
                attempts: List[Dict[str, Any]] = []
                is_windows = platform.system().lower().startswith("win")

                # 使用集合来跟踪已添加的配置，确保唯一性
                unique_attempts = set()

                def add_attempt(overrides: Dict[str, Any]):
                    """安全地添加一个不重复的尝试配置"""
                    # 将字典转换为不可变的 frozenset 以便存入集合
                    key = frozenset(overrides.items())
                    if key not in unique_attempts:
                        unique_attempts.add(key)
                        attempts.append(overrides)

                if is_windows:
                    # Windows 平台策略：优先使用已知最稳定的参数组合
                    # 1. 极度保守：禁用mmap、禁用GPU、低上下文、低批次、单线程
                    add_attempt({"use_mmap": False, "n_gpu_layers": 0, "n_ctx": 4096, "n_batch": 64, "n_threads": 1})
                    # 2. 通用安全：禁用mmap、禁用GPU
                    add_attempt({"use_mmap": False, "n_gpu_layers": 0})
                    # 3. 仅禁用mmap（常见修复手段）
                    add_attempt({"use_mmap": False})
                    # 4. 仅禁用GPU
                    add_attempt({"n_gpu_layers": 0})
                    # 5. 尝试用户原始配置
                    add_attempt({})
                else:
                    # 非Windows平台策略：从用户配置开始，逐步增加限制
                    add_attempt({})
                    if config.context_length and config.context_length > 8192:
                        add_attempt({"n_ctx": 8192})
                    if config.use_mmap:
                        add_attempt({"use_mmap": False})
                    if config.n_gpu_layers != 0:
                        add_attempt({"n_gpu_layers": 0})
                
                # 为所有平台添加一个最终的、普遍安全的备用选项
                add_attempt({"n_ctx": 4096, "use_mmap": False, "n_gpu_layers": 0})

                last_error: Optional[Exception] = None
                # 在开始加载循环前，执行一次垃圾回收，以释放最大内存
                gc.collect()
                for idx, override in enumerate(attempts, start=1):
                    try:
                        # 由环境变量控制详细日志，避免依赖特定logger实现
                        _verbose = str(os.environ.get("LLAMA_VERBOSE", "")).lower() in ("1", "true", "yes", "y", "debug")
                        effective_kwargs = dict(
                            model_path=abs_model_path_posix,
                            n_ctx=config.context_length,
                            n_gpu_layers=config.n_gpu_layers,
                            n_threads=config.n_threads,
                            n_batch=config.n_batch,
                            use_mmap=config.use_mmap,
                            use_mlock=config.use_mlock,
                            verbose=_verbose,
                        )
                        # 应用覆盖参数
                        effective_kwargs.update(override)

                        human_overrides = ", ".join(f"{k}={v}" for k, v in override.items()) or "(无)"
                        logger.info(f"尝试加载GGUF模型 (尝试 {idx}/{len(attempts)}): overrides={human_overrides}")

                        self.llama_model = Llama(**effective_kwargs)
                        self.last_effective_kwargs = effective_kwargs.copy()
                        logger.info(f"GGUF模型加载成功: {config.name} (尝试 {idx})")
                        return True
                    except Exception as e_try:
                        last_error = e_try
                        # 特判访问冲突错误，给出可读提示
                        err_msg = str(e_try)
                        if "access violation" in err_msg.lower() or "reading 0x0000000000000000" in err_msg:
                            logger.error("检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…")
                        else:
                            logger.warning(f"本次尝试失败: {err_msg}")

                # 所有尝试均失败
                if last_error:
                    logger.error(
                        "加载GGUF模型失败（已尝试多种安全参数）: %s\n%s",
                        last_error,
                        traceback.format_exc()
                    )
                    # 提示可能原因与动作
                    logger.error(
                        "可能原因与建议:\n"
                        "1) llama-cpp-python 版本与GGUF版本不兼容，请尝试升级/降级 llama-cpp-python。\n"
                        "2) 模型文件损坏，请校验GGUF文件或重新下载。\n"
                        "3) Windows 平台兼容性问题，保持 use_mmap=False 与 n_gpu_layers=0 并尝试更小模型/更低 n_ctx。\n"
                        "4) 若计划启用GPU，请确保已安装带cuBLAS的 llama-cpp-python 轮子，并设置 n_gpu_layers>0。"
                    )
                return False

        except ImportError:
            logger.error("llama-cpp-python未安装，无法加载GGUF模型")
            return False
        except Exception as e:
            logger.error(f"加载GGUF模型失败: {e}")
            return False

    def get_effective_params(self) -> Optional[Dict[str, Any]]:
        """返回最近一次成功加载时使用的有效参数（仅本地GGUF）。"""
        return self.last_effective_kwargs.copy() if self.last_effective_kwargs else None

    def _pre_validate_gguf(self, path: str) -> Tuple[bool, Optional[int]]:
        """快速校验GGUF文件头与版本。返回 (是否有效, 版本号或None)。"""
        with open(path, 'rb') as f:
            header = f.read(8)
            if len(header) < 8:
                return False, None
            if header[:4] != b'GGUF':
                return False, None
            # 版本为接下来的4字节 little-endian uint32
            ver = int.from_bytes(header[4:8], 'little', signed=False)
            return True, ver

    def generate_text(self, messages: List[Dict[str, str]], **kwargs) -> Generator[str, None, None]:
        """生成文本（流式输出）"""
        if not self.llama_model:
            yield "错误: GGUF模型未加载"
            return

        try:
            # 将消息转换为prompt格式
            prompt = self._messages_to_prompt(messages)

            # 生成参数
            max_tokens = kwargs.get('max_tokens', 2048)
            if max_tokens == -1:
                max_tokens = None

            temperature = kwargs.get('temperature', 0.7)
            top_p = kwargs.get('top_p', 0.9)
            top_k = kwargs.get('top_k', 40)
            repeat_penalty = kwargs.get('repetition_penalty', 1.1)

            # 流式生成
            stream = self.llama_model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                repeat_penalty=repeat_penalty,
                stream=True,
                stop=["</s>", "<|im_end|>", "<|endoftext|>"]
            )

            for output in stream:
                content = output['choices'][0]['text']
                if content:
                    yield content

        except Exception as e:
            logger.error(f"GGUF模型生成文本失败: {e}")
            yield f"错误: {str(e)}"

    def generate_text_complete(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """生成完整文本（非流式）"""
        if not self.llama_model:
            return "错误: GGUF模型未加载"

        try:
            # 将消息转换为prompt格式
            prompt = self._messages_to_prompt(messages)

            # 生成参数
            max_tokens = kwargs.get('max_tokens', 2048)
            if max_tokens == -1:
                max_tokens = None

            temperature = kwargs.get('temperature', 0.7)
            top_p = kwargs.get('top_p', 0.9)
            top_k = kwargs.get('top_k', 40)
            repeat_penalty = kwargs.get('repetition_penalty', 1.1)

            # 生成文本
            output = self.llama_model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                top_k=top_k,
                repeat_penalty=repeat_penalty,
                stream=False,
                stop=["</s>", "<|im_end|>", "<|endoftext|>"]
            )

            return output['choices'][0]['text']

        except Exception as e:
            logger.error(f"GGUF模型生成文本失败: {e}")
            return f"错误: {str(e)}"

    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为prompt格式"""
        prompt_parts = []

        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')

            if role == 'system':
                prompt_parts.append(f"System: {content}")
            elif role == 'user':
                prompt_parts.append(f"User: {content}")
            elif role == 'assistant':
                prompt_parts.append(f"Assistant: {content}")

        prompt_parts.append("Assistant:")
        return "\n".join(prompt_parts)

    def unload_model(self):
        """卸载模型"""
        with self.model_lock:
            if self.llama_model:
                del self.llama_model
                self.llama_model = None
                logger.info("GGUF模型已卸载")
                # 尝试释放底层后端（如果可用）并触发 GC，减少句柄/内存残留
                try:
                    import llama_cpp as _llama_mod
                    if hasattr(_llama_mod, "llama_backend_free"):
                        _llama_mod.llama_backend_free()
                        logger.debug("llama.cpp 后端已释放")
                except Exception as e_free:
                    logger.debug(f"llama.cpp 后端释放失败（忽略）: {e_free}")
                gc.collect()

    def is_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.llama_model is not None

class LLMModelManager:
    """LLM模型管理器（支持多种加载模式）"""

    def __init__(self, config_path: str = "config/settings/app_config.yaml"):
        self.config_path = config_path
        self.client: Optional[OpenAI] = None
        self.current_config: Optional[ModelConfig] = None
        self.model_lock = threading.Lock()
        self.available_models: Dict[str, Dict[str, Any]] = {}

        # 初始化加载器
        self.local_loader = LocalGGUFLoader()
        self.current_load_mode = LoadMode.SERVER

        self._load_config()
        self._scan_models()  # 扫描本地模型文件
        self._initialize_client() # 初始化客户端

    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                self.llm_config = config.get('models', {}).get('llm', {})
                logger.info("配置文件加载成功")
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self.llm_config = {}
    
    def _scan_models(self):
        """扫描本地模型目录，检测格式并推荐加载方式"""
        model_path = self.llm_config.get('model_path', 'models/llm')
        if not os.path.exists(model_path):
            logger.warning(f"模型目录不存在: {model_path}")
            return

        supported_extensions = ['.gguf', '.safetensors', '.bin', '.pt', '.pth']

        for file in os.listdir(model_path):
            file_path = os.path.join(model_path, file)
            if os.path.isfile(file_path):
                # 检查文件扩展名
                if any(file.lower().endswith(ext) for ext in supported_extensions):
                    # 检测模型格式
                    format = ModelFormatDetector.detect_format(file_path)
                    recommended_mode = ModelFormatDetector.get_recommended_load_mode(format)

                    # 生成模型名称（去掉扩展名）
                    model_name = os.path.splitext(file)[0]

                    self.available_models[model_name] = {
                        'path': file_path,
                        'format': format,
                        'recommended_mode': recommended_mode,
                        'supports_local': ModelFormatDetector.is_supported_for_local_loading(format)
                    }

                    logger.info(f"发现模型: {model_name} (格式: {format.value}, 推荐模式: {recommended_mode.value})")

    def _initialize_client(self):
        """初始化OpenAI客户端以连接到llama.cpp服务器"""
        server_url = self.llm_config.get('server_url', 'http://localhost:8080/v1')
        with self.model_lock:
            try:
                self.client = OpenAI(
                    base_url=server_url,
                    api_key="not-needed"  # llama.cpp服务器不需要API密钥
                )
                logger.info(f"已连接到LLM服务器: {server_url}")
            except Exception as e:
                logger.error(f"连接LLM服务器失败: {e}")
                self.client = None

    def get_available_models(self) -> Dict[str, Dict[str, Any]]:
        """获取可用模型信息列表"""
        return self.available_models.copy()

    def load_model(self, model_name: str, load_mode: Optional[LoadMode] = None, **kwargs) -> bool:
        """加载模型（支持多种加载模式）"""
        if model_name not in self.available_models:
            logger.error(f"模型不存在: {model_name}")
            return False

        model_info = self.available_models[model_name]
        model_path = model_info['path']
        model_format = model_info['format']

        # 确定加载模式
        if load_mode is None:
            load_mode = model_info['recommended_mode']
        elif load_mode == LoadMode.AUTO:
            load_mode = model_info['recommended_mode']

        # 检查加载模式是否支持
        if load_mode == LoadMode.LOCAL and not model_info['supports_local']:
            logger.warning(f"模型 {model_name} 不支持本地加载，切换到服务器模式")
            load_mode = LoadMode.SERVER

        # 创建模型配置
        config = ModelConfig(
            name=model_name,
            path=model_path,
            format=model_format,
            load_mode=load_mode,
            context_length=kwargs.get('context_length', self.llm_config.get('max_context_length', 8000)),
            temperature=kwargs.get('temperature', self.llm_config.get('temperature', 0.7)),
            top_p=kwargs.get('top_p', self.llm_config.get('top_p', 0.9)),
            top_k=kwargs.get('top_k', self.llm_config.get('top_k', 40)),
            repetition_penalty=kwargs.get('repetition_penalty', self.llm_config.get('repetition_penalty', 1.1)),
            max_tokens=kwargs.get('max_tokens', self.llm_config.get('max_new_tokens', 2048)),
            n_gpu_layers=kwargs.get('n_gpu_layers', self.llm_config.get('n_gpu_layers', -1)),
            n_threads=kwargs.get('n_threads', self.llm_config.get('n_threads', 4)),
            n_batch=kwargs.get('n_batch', self.llm_config.get('n_batch', 512)),
            use_mmap=kwargs.get('use_mmap', self.llm_config.get('use_mmap', True)),
            use_mlock=kwargs.get('use_mlock', self.llm_config.get('use_mlock', False)),
        )

        # 根据加载模式加载模型
        success = False
        if load_mode == LoadMode.LOCAL:
            success = self._load_model_local(config)
        else:  # SERVER mode
            success = self._load_model_server(config)

        if success:
            with self.model_lock:
                self.current_config = config
                self.current_load_mode = load_mode
            logger.info(f"模型 {model_name} 加载成功 (模式: {load_mode.value})")

        return success

    def _load_model_local(self, config: ModelConfig) -> bool:
        """本地加载模型"""
        try:
            # 卸载之前的本地模型
            self.local_loader.unload_model()

            # 加载新模型
            return self.local_loader.load_model(config)
        except Exception as e:
            logger.error(f"本地加载模型失败: {e}")
            return False

    def _load_model_server(self, config: ModelConfig) -> bool:
        """服务器模式加载模型（仅设置配置）"""
        try:
            # 卸载本地模型（如果有）
            self.local_loader.unload_model()

            # 服务器模式下只需要设置配置，实际模型由服务器管理
            logger.info(f"服务器模式: 配置模型 {config.name}")
            return True
        except Exception as e:
            logger.error(f"服务器模式配置失败: {e}")
            return False

    def generate_text(self, messages: List[Dict[str, str]], **kwargs) -> Generator[str, None, None]:
        """生成文本（流式输出，支持多种模式）"""
        if not self.current_config:
            yield "错误: 未选择模型"
            return

        # 根据当前加载模式选择生成方式
        if self.current_load_mode == LoadMode.LOCAL:
            yield from self._generate_text_local(messages, **kwargs)
        else:  # SERVER mode
            yield from self._generate_text_server(messages, **kwargs)

    def _generate_text_local(self, messages: List[Dict[str, str]], **kwargs) -> Generator[str, None, None]:
        """本地模式生成文本"""
        if not self.local_loader.is_loaded():
            yield "错误: 本地模型未加载"
            return

        yield from self.local_loader.generate_text(messages, **kwargs)

    def _generate_text_server(self, messages: List[Dict[str, str]], **kwargs) -> Generator[str, None, None]:
        """服务器模式生成文本"""
        if not self.client:
            yield "错误: LLM客户端未初始化"
            return

        try:
            max_tokens = kwargs.get('max_tokens', self.current_config.max_tokens)
            if max_tokens == -1:
                max_tokens = None # OpenAI SDK 中 None 表示无限制

            stream = self.client.chat.completions.create(
                model=self.current_config.name, # 模型名称需要与服务器加载的匹配
                messages=messages,
                temperature=kwargs.get('temperature', self.current_config.temperature),
                top_p=kwargs.get('top_p', self.current_config.top_p),
                max_tokens=max_tokens,
                stream=True
            )
            for chunk in stream:
                content = chunk.choices[0].delta.content
                if content:
                    yield content
        except Exception as e:
            error_msg = str(e)
            # 处理编码错误和网络错误
            try:
                error_msg = error_msg.encode('utf-8', errors='ignore').decode('utf-8')
            except:
                error_msg = "文本生成时出现未知错误"

            # 特殊处理502错误
            if "502" in error_msg or "Bad Gateway" in error_msg:
                error_msg = "服务器连接失败(502)，请检查llama.cpp服务器是否正常运行"
            elif "Connection" in error_msg:
                error_msg = "无法连接到LLM服务器，请检查服务器状态"

            logger.error(f"文本生成失败: {error_msg}")
            yield f"错误: {error_msg}"

    def generate_text_complete(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """生成完整文本（非流式，支持多种模式）"""
        if not self.current_config:
            return "错误: 未选择模型"

        # 根据当前加载模式选择生成方式
        if self.current_load_mode == LoadMode.LOCAL:
            return self._generate_text_complete_local(messages, **kwargs)
        else:  # SERVER mode
            return self._generate_text_complete_server(messages, **kwargs)

    def _generate_text_complete_local(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """本地模式生成完整文本"""
        if not self.local_loader.is_loaded():
            return "错误: 本地模型未加载"

        return self.local_loader.generate_text_complete(messages, **kwargs)

    def _generate_text_complete_server(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """服务器模式生成完整文本"""
        if not self.client:
            return "错误: LLM客户端未初始化"

        try:
            max_tokens = kwargs.get('max_tokens', self.current_config.max_tokens)
            if max_tokens == -1:
                max_tokens = None # OpenAI SDK 中 None 表示无限制

            completion = self.client.chat.completions.create(
                model=self.current_config.name,
                messages=messages,
                temperature=kwargs.get('temperature', self.current_config.temperature),
                top_p=kwargs.get('top_p', self.current_config.top_p),
                max_tokens=max_tokens,
                stream=False
            )
            return completion.choices[0].message.content
        except Exception as e:
            error_msg = str(e)
            # 处理编码错误和网络错误
            try:
                error_msg = error_msg.encode('utf-8', errors='ignore').decode('utf-8')
            except:
                error_msg = "文本生成时出现未知错误"

            # 特殊处理502错误
            if "502" in error_msg or "Bad Gateway" in error_msg:
                error_msg = "服务器连接失败(502)，请检查llama.cpp服务器是否正常运行"
            elif "Connection" in error_msg:
                error_msg = "无法连接到LLM服务器，请检查服务器状态"

            logger.error(f"文本生成失败: {error_msg}")
            return f"错误: {error_msg}"

    def get_current_model_info(self) -> Optional[Dict[str, Any]]:
        """获取当前模型配置信息"""
        if not self.current_config:
            return None

        info = self.current_config.__dict__.copy()
        info['current_load_mode'] = self.current_load_mode.value
        info['local_model_loaded'] = self.local_loader.is_loaded()
        info['server_connected'] = self.client is not None
        return info

    def unload_model(self):
        """卸载当前模型"""
        with self.model_lock:
            # 卸载本地模型
            self.local_loader.unload_model()

            # 断开服务器连接
            if self.client:
                self.client.close()
                self.client = None

            self.current_config = None
            self.current_load_mode = LoadMode.SERVER
            logger.info("模型已卸载")

    def reload_models(self):
        """重新扫描模型并重新连接客户端"""
        self.available_models.clear()
        self._scan_models()
        self._initialize_client()
        logger.info("模型列表已刷新并已重新连接客户端")

    def get_load_mode_info(self) -> Dict[str, Any]:
        """获取加载模式信息"""
        return {
            'current_mode': self.current_load_mode.value,
            'available_modes': [mode.value for mode in LoadMode],
            'local_loader_available': True,  # llama-cpp-python已安装
            'server_connected': self.client is not None
        }

    def switch_load_mode(self, mode: LoadMode) -> bool:
        """切换加载模式（需要重新加载模型）"""
        if not self.current_config:
            logger.warning("没有加载的模型，无法切换模式")
            return False

        model_name = self.current_config.name
        logger.info(f"切换加载模式: {self.current_load_mode.value} -> {mode.value}")

        # 重新加载模型
        return self.load_model(model_name, load_mode=mode)

# 全局模型管理器实例
model_manager = LLMModelManager()
