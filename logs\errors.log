2025-08-10 10:02:52 | ERROR    | core.llm.model_manager:load_model:161 | 加载GGUF模型失败: exception: access violation reading 0x0000000000000000
2025-08-10 10:49:17 | ERROR    | core.llm.model_manager:load_model:161 | 加载GGUF模型失败: exception: access violation reading 0x0000000000000000
2025-08-10 11:24:40 | ERROR    | core.llm.model_manager:load_model:218 | 加载GGUF模型失败（已尝试多种安全参数）: %s
%s
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:225 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 11:27:15 | ERROR    | core.llm.model_manager:load_model:231 | 加载GGUF模型失败（已尝试多种安全参数）: %s
%s
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:243 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:249 | 加载GGUF模型失败（已尝试多种安全参数）: %s
%s
2025-08-10 15:56:37 | ERROR    | core.llm.model_manager:load_model:255 | 可能原因与建议:
1) llama-cpp-python 版本与GGUF版本不兼容，请尝试升级/降级 llama-cpp-python。
2) 模型文件损坏，请校验GGUF文件或重新下载。
3) Windows 平台兼容性问题，保持 use_mmap=False 与 n_gpu_layers=0 并尝试更小模型/更低 n_ctx。
4) 若计划启用GPU，请确保已安装带cuBLAS的 llama-cpp-python 轮子，并设置 n_gpu_layers>0。
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:254 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:260 | 加载GGUF模型失败（已尝试多种安全参数）: %s
%s
2025-08-10 17:01:59 | ERROR    | core.llm.model_manager:load_model:266 | 可能原因与建议:
1) llama-cpp-python 版本与GGUF版本不兼容，请尝试升级/降级 llama-cpp-python。
2) 模型文件损坏，请校验GGUF文件或重新下载。
3) Windows 平台兼容性问题，保持 use_mmap=False 与 n_gpu_layers=0 并尝试更小模型/更低 n_ctx。
4) 若计划启用GPU，请确保已安装带cuBLAS的 llama-cpp-python 轮子，并设置 n_gpu_layers>0。
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:259 | 检测到 Windows 访问冲突，已自动切换更安全的加载参数后继续重试…
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:265 | 加载GGUF模型失败（已尝试多种安全参数）: %s
%s
2025-08-10 17:17:37 | ERROR    | core.llm.model_manager:load_model:271 | 可能原因与建议:
1) llama-cpp-python 版本与GGUF版本不兼容，请尝试升级/降级 llama-cpp-python。
2) 模型文件损坏，请校验GGUF文件或重新下载。
3) Windows 平台兼容性问题，保持 use_mmap=False 与 n_gpu_layers=0 并尝试更小模型/更低 n_ctx。
4) 若计划启用GPU，请确保已安装带cuBLAS的 llama-cpp-python 轮子，并设置 n_gpu_layers>0。
